<?xml version="1.0" encoding="utf-8"?>
<layout>

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/main_home_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black"
        android:fitsSystemWindows="false"
        tools:context=".ui.activity.MainHomeActivity">

        <!-- Main Camera Preview -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextureView
                android:id="@+id/main_camera_texture_preview"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <cmc.ccam.terminal.widget.FaceRectView
                android:id="@+id/main_camera_face_rect_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <!-- IR Camera Preview (if available) -->
            <FrameLayout
                android:id="@+id/fl_ir_preview"
                android:layout_width="200dp"
                android:layout_height="150dp"
                android:layout_gravity="bottom|end"
                android:layout_margin="16dp"
                android:visibility="gone">

                <TextureView
                    android:id="@+id/main_camera_texture_preview_ir"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <cmc.ccam.terminal.widget.FaceRectView
                    android:id="@+id/main_camera_face_rect_view_ir"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </FrameLayout>

        </FrameLayout>

        <!-- Top Status Bar -->
        <LinearLayout
            android:id="@+id/top_status_bar"
            android:layout_width="match_parent"
            android:layout_height="75dp"
            android:layout_gravity="top"
            android:background="#B3373E47"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="24dp">

            <!-- CMC-CAM Logo/Title -->
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="CMC-CAM"
                android:textColor="#008FD3"
                android:textSize="24sp"
                android:textStyle="bold" />

            <!-- System Status Icons -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Database Status -->
                <ImageView
                    android:id="@+id/iv_database_status"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/status_indicator_background"
                    android:contentDescription="Database Status"
                    android:src="@drawable/ic_database" />

                <!-- Network Status -->
                <ImageView
                    android:id="@+id/iv_network_status"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/status_indicator_background"
                    android:contentDescription="Network Status"
                    android:src="@drawable/ic_network" />

                <!-- Server Status -->
                <ImageView
                    android:id="@+id/iv_server_status"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/status_indicator_background"
                    android:contentDescription="Server Status"
                    android:src="@drawable/ic_server" />

                <!-- Settings Button -->
                <ImageView
                    android:id="@+id/iv_settings"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="@drawable/status_indicator_background"
                    android:clickable="true"
                    android:contentDescription="Settings"
                    android:focusable="true"
                    android:src="@drawable/ic_settings" />

            </LinearLayout>

        </LinearLayout>

        <!-- Bottom Status Panel -->
        <LinearLayout
            android:id="@+id/bottom_status_panel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="#B31E293B"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Time Display -->
            <TextView
                android:id="@+id/tv_current_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Thứ 6, Ngày 31 Tháng 7, 2025 - 14:30:00"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- Recognition Status -->
            <TextView
                android:id="@+id/tv_recognition_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text=""
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:visibility="gone" />

            <!-- Face Quality Indicator -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Face Quality: "
                    android:textColor="@android:color/white"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_face_quality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0%"
                    android:textColor="#34D38D"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <!-- Recognition Results Display -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_recognition_results"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="16dp"
            android:visibility="gone"
            tools:listitem="@layout/item_compare_result" />

    </FrameLayout>

</layout>
