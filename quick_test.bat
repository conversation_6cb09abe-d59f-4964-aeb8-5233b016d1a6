@echo off
echo ========================================
echo   Quick Settings Test
echo ========================================
echo.

set PACKAGE_NAME=cmc.ccam.terminal

echo [1/3] Checking if app is installed...
adb shell pm list packages | findstr %PACKAGE_NAME%
if %ERRORLEVEL% neq 0 (
    echo App not installed. Please install first.
    pause
    exit /b 1
)

echo.
echo [2/3] Starting test settings activity...
adb shell am start -n %PACKAGE_NAME%/.ui.activity.TestSettingsActivity

echo.
echo [3/3] Monitoring logs for 5 seconds...
timeout /t 2 /nobreak > nul
adb logcat -c
timeout /t 3 /nobreak > nul

echo.
echo === SETTINGS LOGS ===
adb logcat -d | findstr -i "TestSettings\|RecognizeSettings\|preference\|error\|exception"

echo.
echo ========================================
echo   Test completed!
echo ========================================
pause
