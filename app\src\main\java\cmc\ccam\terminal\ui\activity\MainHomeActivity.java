package cmc.ccam.terminal.ui.activity;

import android.Manifest;
import android.content.Intent;
import android.graphics.Point;
import android.hardware.Camera;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.WindowManager;

import androidx.core.app.ActivityCompat;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.ViewModelProvider;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;

import cmc.ccam.terminal.R;
import cmc.ccam.terminal.databinding.ActivityMainHomeBinding;
import cmc.ccam.terminal.ui.model.PreviewConfig;
import cmc.ccam.terminal.ui.viewmodel.MainHomeViewModel;
import cmc.ccam.terminal.util.ConfigUtil;
import cmc.ccam.terminal.util.FaceRectTransformer;
import cmc.ccam.terminal.util.camera.CameraListener;
import cmc.ccam.terminal.util.camera.DualCameraHelper;
import cmc.ccam.terminal.util.face.constants.LivenessType;
import cmc.ccam.terminal.util.face.model.FacePreviewInfo;
import cmc.ccam.terminal.widget.FaceRectView;

/**
 * 主页面 - 专注于人脸检测和识别的主界面
 */
public class MainHomeActivity extends BaseActivity implements ViewTreeObserver.OnGlobalLayoutListener, View.OnClickListener {

    private static final String TAG = "MainHomeActivity";
    private ActivityMainHomeBinding binding;
    private MainHomeViewModel mainHomeViewModel;

    private DualCameraHelper rgbCameraHelper;
    private DualCameraHelper irCameraHelper;
    private FaceRectTransformer rgbFaceRectTransformer;
    private FaceRectTransformer irFaceRectTransformer;

    private LivenessType livenessType;
    private boolean enableLivenessDetect;

    private Timer clockTimer;
    private SimpleDateFormat timeFormat;

    private static final String[] NEEDED_PERMISSIONS = new String[]{
            Manifest.permission.CAMERA,
            Manifest.permission.READ_PHONE_STATE
    };

    private static final int ACTION_REQUEST_PERMISSIONS = 0x001;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Enable full-screen immersive mode
        enableFullScreenMode();
        
        binding = DataBindingUtil.setContentView(this, R.layout.activity_main_home);
        
        initData();
        initViewModel();
        initView();
        initClock();
        
        if (!checkPermissions(NEEDED_PERMISSIONS)) {
            ActivityCompat.requestPermissions(this, NEEDED_PERMISSIONS, ACTION_REQUEST_PERMISSIONS);
        } else {
            mainHomeViewModel.init();
            initCamera();
        }
    }

    private void enableFullScreenMode() {
        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
        );
        
        // Hide navigation bar and status bar
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        );
    }

    private void initData() {
        String livenessTypeStr = ConfigUtil.getLivenessDetectType(this);
        if (livenessTypeStr.equals(getString(R.string.value_liveness_type_rgb))) {
            livenessType = LivenessType.RGB;
        } else if (livenessTypeStr.equals(getString(R.string.value_liveness_type_ir))) {
            livenessType = LivenessType.IR;
        } else {
            livenessType = null;
        }
        enableLivenessDetect = !ConfigUtil.getLivenessDetectType(this).equals(getString(R.string.value_liveness_type_disable));
    }

    private void initViewModel() {
        mainHomeViewModel = new ViewModelProvider(
                getViewModelStore(),
                new ViewModelProvider.AndroidViewModelFactory(getApplication())
        ).get(MainHomeViewModel.class);

        mainHomeViewModel.setLiveType(livenessType);

        // Observe recognition status
        mainHomeViewModel.getRecognitionStatus().observe(this, status -> {
            binding.tvRecognitionStatus.setText(status);
            binding.tvRecognitionStatus.setVisibility(
                (status == null || status.isEmpty()) ? View.GONE : View.VISIBLE
            );
        });

        // Observe face quality
        mainHomeViewModel.getFaceQuality().observe(this, quality -> {
            binding.tvFaceQuality.setText(quality);
        });

        // Observe recognition results
        mainHomeViewModel.getRecognitionResults().observe(this, results -> {
            // For now, just show/hide the RecyclerView based on results
            binding.rvRecognitionResults.setVisibility(
                (results == null || results.isEmpty()) ? View.GONE : View.VISIBLE
            );
        });
    }

    private void initView() {
        // Hide IR preview if not available or not using IR liveness
        if (!DualCameraHelper.hasDualCamera() || livenessType != LivenessType.IR) {
            binding.flIrPreview.setVisibility(View.GONE);
        }

        // Set up click listeners
        binding.ivSettings.setOnClickListener(this);

        // Set up camera preview layout listener
        binding.mainCameraTexturePreview.getViewTreeObserver().addOnGlobalLayoutListener(this);
    }

    private void initClock() {
        timeFormat = new SimpleDateFormat("EEEE, 'Ngày' dd 'Tháng' MM, yyyy - HH:mm:ss", new Locale("vi", "VN"));
        
        clockTimer = new Timer();
        clockTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                runOnUiThread(() -> {
                    String currentTime = timeFormat.format(new Date());
                    binding.tvCurrentTime.setText(currentTime);
                });
            }
        }, 0, 1000); // Update every second
    }

    private void initCamera() {
        initRgbCamera();
        if (DualCameraHelper.hasDualCamera() && livenessType == LivenessType.IR) {
            initIrCamera();
        }
    }

    private void initRgbCamera() {
        Log.i(TAG, "initRgbCamera called");

        // Check if TextureView is ready
        if (!binding.mainCameraTexturePreview.isAvailable()) {
            Log.w(TAG, "TextureView not available yet, setting up listener");
            binding.mainCameraTexturePreview.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
                @Override
                public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
                    Log.i(TAG, "TextureView surface available: " + width + "x" + height);
                    binding.mainCameraTexturePreview.setSurfaceTextureListener(null);
                    initRgbCameraInternal();
                }

                @Override
                public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {}

                @Override
                public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) { return false; }

                @Override
                public void onSurfaceTextureUpdated(SurfaceTexture surface) {}
            });
            return;
        }

        initRgbCameraInternal();
    }

    private void initRgbCameraInternal() {
        Log.i(TAG, "initRgbCameraInternal called");
        CameraListener cameraListener = new CameraListener() {
            @Override
            public void onCameraOpened(Camera camera, int cameraId, int displayOrientation, boolean isMirror) {
                Log.i(TAG, "RGB Camera opened - CameraId: " + cameraId + ", DisplayOrientation: " + displayOrientation);
                runOnUiThread(() -> {
                    Camera.Size previewSizeRgb = camera.getParameters().getPreviewSize();
                    Log.i(TAG, "RGB Preview size: " + previewSizeRgb.width + "x" + previewSizeRgb.height);

                    ViewGroup.LayoutParams layoutParams = adjustPreviewViewSize(
                            binding.mainCameraTexturePreview,
                            binding.mainCameraTexturePreview,
                            binding.mainCameraFaceRectView,
                            previewSizeRgb, displayOrientation, 1.0f);

                    rgbFaceRectTransformer = new FaceRectTransformer(
                            previewSizeRgb.width, previewSizeRgb.height,
                            layoutParams.width, layoutParams.height,
                            displayOrientation, cameraId, isMirror,
                            ConfigUtil.isDrawRgbRectHorizontalMirror(MainHomeActivity.this),
                            ConfigUtil.isDrawRgbRectVerticalMirror(MainHomeActivity.this)
                    );

                    mainHomeViewModel.onRgbCameraOpened(camera);
                    mainHomeViewModel.setRgbFaceRectTransformer(rgbFaceRectTransformer);

                    // Force TextureView to refresh
                    binding.mainCameraTexturePreview.requestLayout();
                    binding.mainCameraTexturePreview.invalidate();

                    Log.i(TAG, "RGB Camera setup completed");
                });
            }

            @Override
            public void onPreview(final byte[] nv21, Camera camera) {
                if (nv21 != null) {
                    // Log every 30 frames to avoid spam
                    if (System.currentTimeMillis() % 1000 < 50) {
                        Log.d(TAG, "Received preview frame, size: " + nv21.length);
                    }
                }

                binding.mainCameraFaceRectView.clearFaceInfo();
                List<FacePreviewInfo> facePreviewInfoList = mainHomeViewModel.onPreviewFrame(nv21, true);
                if (facePreviewInfoList != null && rgbFaceRectTransformer != null) {
                    if (System.currentTimeMillis() % 1000 < 50) {
                        Log.d(TAG, "Drawing " + facePreviewInfoList.size() + " faces");
                    }
                    drawPreviewInfo(facePreviewInfoList);
                }
                mainHomeViewModel.clearLeftFace(facePreviewInfoList);
            }

            @Override
            public void onCameraClosed() {
                Log.i(TAG, "RGB Camera closed");
            }

            @Override
            public void onCameraError(Exception e) {
                Log.e(TAG, "RGB Camera error: " + e.getMessage());
                e.printStackTrace();
            }

            @Override
            public void onCameraConfigurationChanged(int cameraID, int displayOrientation) {
                if (rgbFaceRectTransformer != null) {
                    rgbFaceRectTransformer.setCameraDisplayOrientation(displayOrientation);
                }
            }
        };

        PreviewConfig previewConfig = mainHomeViewModel.getPreviewConfig();

        int previewWidth = binding.mainCameraTexturePreview.getMeasuredWidth();
        int previewHeight = binding.mainCameraTexturePreview.getMeasuredHeight();
        Log.i(TAG, "TextureView measured size: " + previewWidth + "x" + previewHeight);

        rgbCameraHelper = new DualCameraHelper.Builder()
                .previewViewSize(new Point(previewWidth, previewHeight))
                .rotation(getWindowManager().getDefaultDisplay().getRotation())
                .additionalRotation(previewConfig.getRgbAdditionalDisplayOrientation())
                .previewSize(mainHomeViewModel.loadPreviewSize())
                .specificCameraId(previewConfig.getRgbCameraId())
                .isMirror(ConfigUtil.isDrawRgbPreviewHorizontalMirror(this))
                .previewOn(binding.mainCameraTexturePreview)
                .cameraListener(cameraListener)
                .build();

        Log.i(TAG, "Starting RGB camera...");
        Log.i(TAG, "TextureView isAvailable: " + binding.mainCameraTexturePreview.isAvailable());
        Log.i(TAG, "TextureView SurfaceTexture: " + binding.mainCameraTexturePreview.getSurfaceTexture());

        try {
            rgbCameraHelper.init();
            rgbCameraHelper.start();
            Log.i(TAG, "RGB camera started successfully");

            // Debug camera state after a short delay
            binding.mainCameraTexturePreview.postDelayed(() -> debugCameraState(), 1000);
        } catch (Exception e) {
            Log.e(TAG, "Failed to start RGB camera: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void initIrCamera() {
        // IR camera implementation will be added in next iteration
        // For now, just log that IR camera initialization was called
        Log.i(TAG, "IR Camera initialization called");
    }

    private void drawPreviewInfo(List<FacePreviewInfo> facePreviewInfoList) {
        if (rgbFaceRectTransformer != null && mainHomeViewModel != null) {
            List<FaceRectView.DrawInfo> drawInfoList = mainHomeViewModel.getDrawInfo(facePreviewInfoList, LivenessType.RGB);
            binding.mainCameraFaceRectView.drawRealtimeFaceInfo(drawInfoList);
        }
    }

    private void debugCameraState() {
        Log.d(TAG, "=== Camera Debug Info ===");
        Log.d(TAG, "rgbCameraHelper: " + (rgbCameraHelper != null ? "initialized" : "null"));
        Log.d(TAG, "TextureView isAvailable: " + binding.mainCameraTexturePreview.isAvailable());
        Log.d(TAG, "TextureView visibility: " + binding.mainCameraTexturePreview.getVisibility());
        Log.d(TAG, "TextureView size: " + binding.mainCameraTexturePreview.getWidth() + "x" + binding.mainCameraTexturePreview.getHeight());
        Log.d(TAG, "TextureView measured size: " + binding.mainCameraTexturePreview.getMeasuredWidth() + "x" + binding.mainCameraTexturePreview.getMeasuredHeight());
        if (rgbCameraHelper != null) {
            Log.d(TAG, "Camera ID: " + rgbCameraHelper.getCameraId());
        }
        Log.d(TAG, "========================");
    }

    @Override
    public void onGlobalLayout() {
        binding.mainCameraTexturePreview.getViewTreeObserver().removeOnGlobalLayoutListener(this);
        if (!checkPermissions(NEEDED_PERMISSIONS)) {
            ActivityCompat.requestPermissions(this, NEEDED_PERMISSIONS, ACTION_REQUEST_PERMISSIONS);
        } else {
            mainHomeViewModel.init();
            initCamera();
        }
    }

    @Override
    protected void afterRequestPermission(int requestCode, boolean isAllGranted) {
        if (requestCode == ACTION_REQUEST_PERMISSIONS) {
            if (isAllGranted) {
                mainHomeViewModel.init();
                initCamera();
            } else {
                showToast(getString(R.string.permission_denied));
            }
        }
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.iv_settings) {
            // Navigate to settings
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivity(intent);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        resumeCamera();
        if (clockTimer == null) {
            initClock();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        pauseCamera();
        if (clockTimer != null) {
            clockTimer.cancel();
            clockTimer = null;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mainHomeViewModel != null) {
            mainHomeViewModel.destroy();
        }
        releaseCamera();
        if (clockTimer != null) {
            clockTimer.cancel();
            clockTimer = null;
        }
    }

    private void resumeCamera() {
        if (rgbCameraHelper != null) {
            rgbCameraHelper.start();
        }
        if (irCameraHelper != null) {
            irCameraHelper.start();
        }
    }

    private void pauseCamera() {
        if (rgbCameraHelper != null) {
            rgbCameraHelper.stop();
        }
        if (irCameraHelper != null) {
            irCameraHelper.stop();
        }
    }

    private void releaseCamera() {
        if (rgbCameraHelper != null) {
            rgbCameraHelper.release();
            rgbCameraHelper = null;
        }
        if (irCameraHelper != null) {
            irCameraHelper.release();
            irCameraHelper = null;
        }
    }

    /**
     * 调整View的宽高，使预览正确显示
     *
     * @param rgbPreview         RGB预览view
     * @param previewView        显示预览数据的view
     * @param faceRectView       画框的view
     * @param previewSize        预览大小
     * @param displayOrientation 相机旋转角度
     * @param scale              缩放比例
     * @return 调整后的LayoutParams
     */
    private ViewGroup.LayoutParams adjustPreviewViewSize(View rgbPreview, View previewView, FaceRectView faceRectView, Camera.Size previewSize, int displayOrientation, float scale) {
        ViewGroup.LayoutParams layoutParams = previewView.getLayoutParams();
        int measuredWidth = previewView.getMeasuredWidth();
        int measuredHeight = previewView.getMeasuredHeight();
        float ratio = ((float) previewSize.height) / (float) previewSize.width;
        if (ratio > 1) {
            ratio = 1 / ratio;
        }
        if (displayOrientation % 180 == 0) {
            layoutParams.width = measuredWidth;
            layoutParams.height = (int) (measuredWidth * ratio);
        } else {
            layoutParams.height = measuredHeight;
            layoutParams.width = (int) (measuredHeight * ratio);
        }
        if (scale < 1f) {
            layoutParams.width = (int) (layoutParams.width * scale);
            layoutParams.height = (int) (layoutParams.height * scale);
        }
        Log.i(TAG, "adjustPreviewViewSize: " + layoutParams.width + "x" + layoutParams.height);
        previewView.setLayoutParams(layoutParams);
        faceRectView.setLayoutParams(layoutParams);
        return layoutParams;
    }
}
