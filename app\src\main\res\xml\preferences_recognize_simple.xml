<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:app="http://schemas.android.com/apk/res-auto">

    <PreferenceCategory app:title="Basic Settings">

        <!-- Simple text preference -->
        <EditTextPreference
            app:key="test_preference"
            app:title="Test Preference"
            app:summary="This is a test preference"
            app:useSimpleSummaryProvider="true" />

        <!-- Simple switch preference -->
        <SwitchPreferenceCompat
            app:defaultValue="true"
            app:key="test_switch"
            app:title="Test Switch"
            app:summary="Enable/disable test feature" />

    </PreferenceCategory>

    <PreferenceCategory app:title="Advanced Settings">

        <!-- Simple list preference with hardcoded values -->
        <ListPreference
            app:key="test_list"
            app:title="Test List"
            app:entries="@array/test_entries"
            app:entryValues="@array/test_values"
            app:defaultValue="option1"
            app:useSimpleSummaryProvider="true" />

    </PreferenceCategory>

</PreferenceScreen>
