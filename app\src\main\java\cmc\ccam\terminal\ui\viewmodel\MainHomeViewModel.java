package cmc.ccam.terminal.ui.viewmodel;

import android.app.Application;
import android.content.Context;
import android.graphics.Point;
import android.graphics.Rect;
import android.hardware.Camera;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.MutableLiveData;

import java.util.ArrayList;
import java.util.List;

import cmc.ccam.terminal.ArcFaceApplication;
import cmc.ccam.terminal.R;
import cmc.ccam.terminal.faceserver.FaceServer;
import cmc.ccam.terminal.ui.model.CompareResult;
import cmc.ccam.terminal.ui.model.PreviewConfig;
import cmc.ccam.terminal.util.ConfigUtil;
import cmc.ccam.terminal.util.FaceRectTransformer;
import cmc.ccam.terminal.util.face.FaceHelper;
import cmc.ccam.terminal.util.face.RecognizeCallback;
import cmc.ccam.terminal.util.face.constants.LivenessType;
import cmc.ccam.terminal.util.face.model.FacePreviewInfo;
import cmc.ccam.terminal.util.face.model.RecognizeConfiguration;
import cmc.ccam.terminal.widget.FaceRectView;
import com.arcsoft.face.ErrorInfo;
import com.arcsoft.face.FaceEngine;
import com.arcsoft.face.enums.DetectFaceOrientPriority;
import com.arcsoft.face.enums.DetectMode;

/**
 * 主页面ViewModel - 专注于人脸检测和识别
 */
public class MainHomeViewModel extends AndroidViewModel {

    private static final String TAG = "MainHomeViewModel";

    private FaceHelper faceHelper;
    private FaceEngine ftEngine;
    private Camera rgbCamera;
    private Camera irCamera;

    private LivenessType livenessType = LivenessType.RGB;
    private boolean loadFaceList = false;
    private byte[] irNV21 = new byte[0];

    private FaceRectTransformer rgbFaceRectTransformer;
    private FaceRectTransformer irFaceRectTransformer;

    private PreviewConfig previewConfig;

    // LiveData for UI updates
    private MutableLiveData<String> recognitionStatus = new MutableLiveData<>();
    private MutableLiveData<String> faceQuality = new MutableLiveData<>();
    private MutableLiveData<List<CompareResult>> recognitionResults = new MutableLiveData<>();

    // Face detection and recognition throttling
    private long lastRecognitionTime = 0;
    private static final long RECOGNITION_THROTTLE_MS = 500; // 500ms throttling

    public MainHomeViewModel(@NonNull Application application) {
        super(application);
        recognitionResults.setValue(new ArrayList<>());
        recognitionStatus.setValue("");
        faceQuality.setValue("0%");
    }

    public void init() {
        if (faceHelper == null) {
            initPreviewConfig();
            initEngine();
        }
    }

    private void initPreviewConfig() {
        Context context = ArcFaceApplication.getApplication();
        boolean switchCamera = ConfigUtil.isSwitchCamera(context);
        previewConfig = new PreviewConfig(
                switchCamera ? Camera.CameraInfo.CAMERA_FACING_FRONT : Camera.CameraInfo.CAMERA_FACING_BACK,
                switchCamera ? Camera.CameraInfo.CAMERA_FACING_BACK : Camera.CameraInfo.CAMERA_FACING_FRONT,
                Integer.parseInt(ConfigUtil.getRgbCameraAdditionalRotation(context)),
                Integer.parseInt(ConfigUtil.getIrCameraAdditionalRotation(context))
        );
    }

    private void initEngine() {
        ftEngine = new FaceEngine();
        int ftInitCode = ftEngine.init(getApplication().getApplicationContext(), DetectMode.ASF_DETECT_MODE_VIDEO,
                ConfigUtil.getFtOrient(getApplication().getApplicationContext()),
                16, FaceEngine.ASF_FACE_DETECT | FaceEngine.ASF_FACE_RECOGNITION | FaceEngine.ASF_AGE | FaceEngine.ASF_GENDER | FaceEngine.ASF_LIVENESS | FaceEngine.ASF_IR_LIVENESS | FaceEngine.ASF_MASK_DETECT);

        Log.i(TAG, "initEngine: init: " + ftInitCode);
        if (ftInitCode != ErrorInfo.MOK) {
            recognitionStatus.postValue("Face engine init failed: " + ftInitCode);
            return;
        }

        initFaceHelper();
        loadFaceList();
    }

    private void initFaceHelper() {
        Context context = ArcFaceApplication.getApplication().getApplicationContext();
        Point previewSize = loadPreviewSize();

        boolean enableFaceMoveLimit = ConfigUtil.isEnableFaceMoveLimit(context);
        boolean enableFaceSizeLimit = ConfigUtil.isEnableFaceSizeLimit(context);
        RecognizeConfiguration recognizeConfiguration = new RecognizeConfiguration.Builder()
                .enableFaceMoveLimit(enableFaceMoveLimit)
                .enableFaceSizeLimit(enableFaceSizeLimit)
                .faceSizeLimit(ConfigUtil.getFaceSizeLimit(context))
                .faceMoveLimit(ConfigUtil.getFaceMoveLimit(context))
                .maxDetectFaces(ConfigUtil.getRecognizeMaxDetectFaceNum(context))
                .keepMaxFace(ConfigUtil.isKeepMaxFace(context))
                .similarThreshold(ConfigUtil.getRecognizeThreshold(context))
                .imageQualityNoMaskRecognizeThreshold(ConfigUtil.getImageQualityNoMaskRecognizeThreshold(context))
                .imageQualityMaskRecognizeThreshold(ConfigUtil.getImageQualityMaskRecognizeThreshold(context))
                .build();

        Camera.Size cameraPreviewSize = new Camera.Size(null, previewSize.x, previewSize.y);
        faceHelper = new FaceHelper.Builder()
                .ftEngine(ftEngine)
                .previewSize(cameraPreviewSize)
                .recognizeConfiguration(recognizeConfiguration)
                .recognizeCallback(new RecognizeCallback() {
                    @Override
                    public void onRecognized(CompareResult compareResult, Integer liveness, boolean similarPass) {
                        if (similarPass) {
                            // Update recognition results
                            List<CompareResult> currentResults = recognitionResults.getValue();
                            if (currentResults == null) {
                                currentResults = new ArrayList<>();
                            }
                            
                            // Add new result or update existing
                            boolean found = false;
                            for (int i = 0; i < currentResults.size(); i++) {
                                if (currentResults.get(i).getTrackId() == compareResult.getTrackId()) {
                                    currentResults.set(i, compareResult);
                                    found = true;
                                    break;
                                }
                            }
                            if (!found) {
                                currentResults.add(compareResult);
                            }
                            
                            recognitionResults.postValue(currentResults);
                            String userName = compareResult.getFaceEntity() != null ? compareResult.getFaceEntity().getUserName() : "Unknown";
                            recognitionStatus.postValue("Recognized: " + userName);
                        }
                    }

                    @Override
                    public void onNoticeChanged(String notice) {
                        recognitionStatus.postValue(notice);
                    }
                })
                .trackedFaceCount(ConfigUtil.getTrackedFaceCount(context))
                .build();
    }

    private void loadFaceList() {
        loadFaceList = false;
        FaceServer.getInstance().init(getApplication().getApplicationContext(), new FaceServer.OnInitFinishedCallback() {
            @Override
            public void onFinished(int faceCount) {
                loadFaceList = true;
                recognitionStatus.postValue("Face database loaded: " + faceCount + " faces");
            }
        });
    }

    public Point loadPreviewSize() {
        String[] size = ConfigUtil.getPreviewSize(getApplication().getApplicationContext()).split("x");
        return new Point(Integer.parseInt(size[0]), Integer.parseInt(size[1]));
    }

    public void setLiveType(LivenessType livenessType) {
        this.livenessType = livenessType;
    }

    public void onRgbCameraOpened(Camera camera) {
        this.rgbCamera = camera;
    }

    public void onIrCameraOpened(Camera camera) {
        this.irCamera = camera;
    }

    public void setRgbFaceRectTransformer(FaceRectTransformer rgbFaceRectTransformer) {
        this.rgbFaceRectTransformer = rgbFaceRectTransformer;
    }

    public void setIrFaceRectTransformer(FaceRectTransformer irFaceRectTransformer) {
        this.irFaceRectTransformer = irFaceRectTransformer;
    }

    /**
     * 处理预览帧数据
     */
    public List<FacePreviewInfo> onPreviewFrame(byte[] nv21, boolean doRecognize) {
        if (faceHelper != null && loadFaceList) {
            // Apply recognition throttling
            long currentTime = System.currentTimeMillis();
            boolean shouldRecognize = doRecognize && (currentTime - lastRecognitionTime) >= RECOGNITION_THROTTLE_MS;
            
            if (shouldRecognize) {
                lastRecognitionTime = currentTime;
            }

            if (livenessType == LivenessType.IR && irNV21 == null) {
                return null;
            }
            
            List<FacePreviewInfo> facePreviewInfoList = faceHelper.onPreviewFrame(nv21, irNV21, shouldRecognize);
            
            // Update face quality
            if (facePreviewInfoList != null && !facePreviewInfoList.isEmpty()) {
                FacePreviewInfo bestFace = facePreviewInfoList.get(0);
                float quality = calculateFaceQuality(bestFace);
                faceQuality.postValue(String.format("%.0f%%", quality * 100));
            } else {
                faceQuality.postValue("0%");
            }
            
            return facePreviewInfoList;
        }
        return null;
    }

    /**
     * 计算人脸质量
     */
    private float calculateFaceQuality(FacePreviewInfo faceInfo) {
        // Simple quality calculation based on face size and position
        if (faceInfo.getFaceInfoRgb() == null) {
            return 0.0f;
        }
        
        Rect faceRect = faceInfo.getFaceInfoRgb().getRect();
        int faceWidth = faceRect.width();
        int faceHeight = faceRect.height();
        
        // Calculate quality based on face size (larger faces = better quality)
        float sizeQuality = Math.min(1.0f, (faceWidth * faceHeight) / (200.0f * 200.0f));
        
        // Apply minimum threshold of 30%
        return Math.max(0.3f, sizeQuality);
    }

    public void clearLeftFace(List<FacePreviewInfo> facePreviewInfoList) {
        // clearLeftFace is called internally by FaceHelper, no need to call it explicitly
    }

    /**
     * 根据预览信息生成绘制信息
     *
     * @param facePreviewInfoList 预览信息
     * @param livenessType        活体类型
     * @return 绘制信息
     */
    public List<FaceRectView.DrawInfo> getDrawInfo(List<FacePreviewInfo> facePreviewInfoList, LivenessType livenessType) {
        List<FaceRectView.DrawInfo> drawInfoList = new ArrayList<>();
        if (faceHelper == null || facePreviewInfoList == null) {
            return drawInfoList;
        }

        for (int i = 0; i < facePreviewInfoList.size(); i++) {
            int trackId = facePreviewInfoList.get(i).getTrackId();
            String name = faceHelper.getName(trackId);
            Integer liveness = faceHelper.getLiveness(trackId);
            Integer recognizeStatus = faceHelper.getRecognizeStatus(trackId);

            // 根据识别结果和活体结果设置颜色
            int color = 0xFF00FF00; // Green for detected faces
            if (recognizeStatus != null) {
                if (recognizeStatus == 0) { // Failed
                    color = 0xFFFF0000; // Red
                } else if (recognizeStatus == 1) { // Success
                    color = 0xFF00FF00; // Green
                }
            }
            if (liveness != null && liveness == 0) { // Not alive
                color = 0xFFFF0000; // Red
            }

            drawInfoList.add(new FaceRectView.DrawInfo(
                    livenessType == LivenessType.RGB ?
                        facePreviewInfoList.get(i).getRgbTransformedRect() :
                        facePreviewInfoList.get(i).getIrTransformedRect(),
                    0, // GenderInfo.UNKNOWN
                    0, // AgeInfo.UNKNOWN_AGE
                    liveness == null ? -1 : liveness, // LivenessInfo
                    color,
                    name == null ? "" : name
            ));
        }
        return drawInfoList;
    }

    public void setRecognizeArea(Rect recognizeArea) {
        if (faceHelper != null) {
            faceHelper.setRecognizeArea(recognizeArea);
        }
    }

    public PreviewConfig getPreviewConfig() {
        return previewConfig;
    }

    // LiveData getters
    public MutableLiveData<String> getRecognitionStatus() {
        return recognitionStatus;
    }

    public MutableLiveData<String> getFaceQuality() {
        return faceQuality;
    }

    public MutableLiveData<List<CompareResult>> getRecognitionResults() {
        return recognitionResults;
    }

    public void destroy() {
        if (faceHelper != null) {
            faceHelper.release();
            faceHelper = null;
        }
        if (ftEngine != null) {
            ftEngine.unInit();
            ftEngine = null;
        }
    }
}
