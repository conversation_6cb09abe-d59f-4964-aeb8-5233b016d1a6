package cmc.ccam.terminal.ui.activity;

import static cmc.ccam.terminal.R.drawable.*;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.ViewModelProvider;

import cmc.ccam.terminal.R;
import cmc.ccam.terminal.databinding.ActivitySettingsBinding;
import cmc.ccam.terminal.ui.viewmodel.HomeViewModel;
import cmc.ccam.terminal.util.ErrorCodeUtil;
import cmc.ccam.terminal.widget.NavigateItemView;
import com.arcsoft.face.ErrorInfo;
import com.arcsoft.face.FaceEngine;
import com.arcsoft.face.VersionInfo;

/**
 * 设置页面，包括激活、界面选择等功能
 */
public class SettingsActivity extends BaseActivity implements View.OnClickListener {
    private ActivitySettingsBinding activitySettingsBinding;
    private static final String TAG = "SettingsActivity";
    private NavigateItemView activeView;

    HomeViewModel homeViewModel;

    private static final String[] NEEDED_PERMISSIONS = new String[]{Manifest.permission.READ_PHONE_STATE};

    private static final int REQUEST_ACTIVE_CODE = 1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        activitySettingsBinding = DataBindingUtil.setContentView(this, R.layout.activity_settings);

        Intent closeusbmodeintent = new Intent("android.intent.action.OTG_MODE");
        sendBroadcast(closeusbmodeintent);

        String androidId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        cmc.ccam.terminal.util.ToastManager.getInstance().showLongToast(this, "androidID: " + androidId);

        initViewModel();
        initView();
        if (checkPermissions(NEEDED_PERMISSIONS)) {
            initData();
        } else {
            ActivityCompat.requestPermissions(this, NEEDED_PERMISSIONS, ACTION_REQUEST_PERMISSIONS);
        }
    }

    private void initData() {
        homeViewModel.getActivated().postValue(homeViewModel.isActivated(this));
    }

    @SuppressLint("StringFormatInvalid")
    private void initViewModel() {
        homeViewModel = new ViewModelProvider(
                getViewModelStore(),
                new ViewModelProvider.AndroidViewModelFactory(getApplication())
        )
                .get(HomeViewModel.class);


        // 设置监听，在数据变更时，更新View中内容
        homeViewModel.getActivated().observe(this, activated
                -> activeView.changeTipHint(getString(activated ? R.string.already_activated : R.string.not_activated))
        );

        homeViewModel.getActiveCode().observe(this, activeCode -> {
            String notification;
            switch (activeCode) {
                case ErrorInfo.MOK:
                    notification = getString(R.string.active_success);
                    break;
                case ErrorInfo.MERR_ASF_ALREADY_ACTIVATED:
                    notification = getString(R.string.dont_need_active_anymore);
                    break;
                default:
                    Log.e("yw",""+activeCode);
                    notification = getString(R.string.active_failed, activeCode, ErrorCodeUtil.arcFaceErrorCodeToFieldName(activeCode));
                    break;
            }
            activeView.changeTipHint(notification);
            showToast(notification);
        });
    }

    private void initView() {
        VersionInfo versionInfo = new VersionInfo();
        if (FaceEngine.getVersion(versionInfo) == ErrorInfo.MOK) {
            activitySettingsBinding.setSdkVersion("ArcFace SDK Version:" + versionInfo.getVersion());
        }

        // Settings-focused navigation items
        activitySettingsBinding.llRootView.addView(new NavigateItemView(this, R.drawable.ic_settings, getString(R.string.page_settings), RecognizeSettingsActivity.class));
        activitySettingsBinding.llRootView.addView(new NavigateItemView(this, R.drawable.ic_face_manage, getString(R.string.page_face_manage), FaceManageActivity.class));
        activitySettingsBinding.llRootView.addView(new NavigateItemView(this, R.drawable.ic_face_id_ir, getString(R.string.page_ir_face_recognize), RegisterAndRecognizeActivity.class));
        activitySettingsBinding.llRootView.addView(new NavigateItemView(this, R.drawable.ic_liveness_check, getString(R.string.page_liveness_detect), LivenessDetectActivity.class));
        activitySettingsBinding.llRootView.addView(new NavigateItemView(this, R.drawable.ic_face_attr, getString(R.string.page_single_image), ImageFaceAttrDetectActivity.class));
        activitySettingsBinding.llRootView.addView(new NavigateItemView(this, R.drawable.ic_face_compare, getString(R.string.page_face_compare), FaceCompareActivity.class));

        activeView = new NavigateItemView(this, R.drawable.ic_online_active, getString(R.string.active_engine), "", ActivationActivity.class);
        activitySettingsBinding.llRootView.addView(activeView);

//        activitySettingsBinding.llRootView.addView(new NavigateItemView(this, R.drawable.ic_readme, getString(R.string.page_readme), ReadmeActivity.class));


        int childCount = activitySettingsBinding.llRootView.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View itemView = activitySettingsBinding.llRootView.getChildAt(i);
            itemView.setOnClickListener(this);
        }
    }


    @Override
    protected void afterRequestPermission(int requestCode, boolean isAllGranted) {
        if (isAllGranted) {
            initData();
        } else {
            showToast(getString(R.string.permission_denied));
        }
    }

    @Override
    public void onClick(View v) {
        if (v instanceof NavigateItemView) {
            if (((NavigateItemView) v).getImgRes() == R.drawable.ic_online_active) {
                navigateToNewPageForResult(((Class) ((NavigateItemView) v).getExtraData()), REQUEST_ACTIVE_CODE);
            } else if (((NavigateItemView) v).getImgRes() == R.drawable.ic_readme) {
                navigateToNewPage(((Class) ((NavigateItemView) v).getExtraData()));
            } else {
                boolean activated = homeViewModel.isActivated(this);
                if (!activated) {
                    showLongToast(getString(R.string.notice_please_active_before_use));
                    activeView.performClick();
                } else {
                    navigateToNewPage(((Class) ((NavigateItemView) v).getExtraData()));
                }
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_ACTIVE_CODE:
                homeViewModel.getActivated().postValue(homeViewModel.isActivated(this));
                break;
            default:
                break;
        }
    }
}
